// src/improved_return_address_finder.rs (Corrected)

// 优化后的返回地址查找器
// 核心优化:
// 1. 缓存系统调用信息 (SSN/Syscall) 和 ROP Gadgets，避免重复查找。
// 2. 将重复的 NtQueryVirtualMemory 调用逻辑（包括重试）抽象为独立的辅助函数。
// 3. 将多次 RtlLookupFunctionEntry 调用合并为一次，并用`FunctionInfo`结构体缓存结果。
// 4. 增强地址验证，通过PE节头检查代码段属性，确保地址在可执行内存区。
// 5. 严格遵循所有系统调用均通过 CallMe 和 CallR12 执行的约束。

use std::collections::HashMap;
use std::ffi::c_void;
use std::mem::size_of;
use std::ptr::null_mut;
use std::sync::Mutex;

use lazy_static::lazy_static;
use windows_sys::Win32::System::Diagnostics::Debug::{
    IMAGE_NT_HEADERS64, IMAGE_OPTIONAL_HEADER64, IMAGE_SECTION_HEADER,
};
use windows_sys::Win32::System::Memory::{
    MEMORY_BASIC_INFORMATION, PAGE_EXECUTE, PAGE_EXECUTE_READ, PAGE_EXECUTE_READWRITE,
    PAGE_EXECUTE_WRITECOPY,
};
use windows_sys::Win32::System::SystemServices::IMAGE_DOS_HEADER;

use crate::hde64::{hde64_disasm, HDE64};
use crate::{
    get_function_address, get_call_r12_gadgets, get_ntdll, go_go_gadget, nt_current_process,
    ssn_lookup, with_logger, CallMe, CallR12, DW_SSN, IMAGE_SCN_MEM_EXECUTE, QW_JMP,
    RUNTIME_FUNCTION, UNWIND_INFO, SyscallEntry,
};

// --- Logging Macros ---
macro_rules! trace_log { ($($arg:tt)*) => { unsafe { with_logger(|logger| logger.trace(&format!("[RET_ADDR_FINDER] {}", format!($($arg)*)))); } }; }
macro_rules! dbg_log { ($($arg:tt)*) => { unsafe { with_logger(|logger| logger.debug(&format!("[RET_ADDR_FINDER] {}", format!($($arg)*)))); } }; }
macro_rules! info_log { ($($arg:tt)*) => { unsafe { with_logger(|logger| logger.info(&format!("[RET_ADDR_FINDER] {}", format!($($arg)*)))); } }; }
macro_rules! warn_log { ($($arg:tt)*) => { unsafe { with_logger(|logger| logger.warn(&format!("[RET_ADDR_FINDER] {}", format!($($arg)*)))); } }; }
macro_rules! error_log { ($($arg:tt)*) => { unsafe { with_logger(|logger| logger.error(&format!("[RET_ADDR_FINDER] {}", format!($($arg)*)))); } }; }


// --- Constants & Caching Structures ---
const ZW_QUERY_VIRTUAL_MEMORY: &str = "ZwQueryVirtualMemory";
const NT_QUERY_VIRTUAL_MEMORY: &str = "NtQueryVirtualMemory";

// Caching `SyscallEntry` can be tricky if it contains raw pointers and needs to be Sync.
// This version stores pointer addresses as `usize`, making it trivially Sync.
#[derive(Copy, Clone)]
struct CachedSyscallEntry {
    ssn: u32,
    syscall: usize,
}

lazy_static! {
    static ref SYSCALL_CACHE: Mutex<HashMap<&'static str, CachedSyscallEntry>> = Mutex::new(HashMap::new());
}

/// Stores info from `RtlLookupFunctionEntry` to avoid redundant calls.
struct FunctionInfo {
    image_base: *mut c_void,
    function_start: *mut c_void,
    function_end: *mut c_void,
    prologue_end: *mut c_void,
    total_size: usize,
}

// --- Core Helper Functions ---

/// Gets syscall info, using a cache to avoid redundant lookups.
fn get_syscall(name: &'static str) -> Option<SyscallEntry> {
    trace_log!("Getting syscall for '{}'", name);
    let mut cache = SYSCALL_CACHE.lock().unwrap();
    if let Some(cached) = cache.get(name) {
        dbg_log!("Cache hit for '{}'", name);
        return Some(SyscallEntry {
            ssn: cached.ssn,
            address: null_mut(), // Address isn't stored/needed after first lookup
            syscall: cached.syscall as *mut c_void,
        });
    }

    dbg_log!("Cache miss for '{}', performing ssn_lookup", name);
    // SAFETY: ssn_lookup is an unsafe function from the library interface.
    let syscall = unsafe { ssn_lookup(name) };

    if syscall.ssn != 0 && !syscall.syscall.is_null() {
        cache.insert(name, CachedSyscallEntry {
            ssn: syscall.ssn,
            syscall: syscall.syscall as usize,
        });
        info_log!("Successfully looked up and cached syscall '{}'", name);
        Some(syscall)
    } else {
        error_log!("Failed to find syscall: '{}'", name);
        None
    }
}

/// [Refactored Core] Safely calls NtQueryVirtualMemory using CallR12, with retries.
/// # SAFETY
/// This function is unsafe because it performs indirect syscalls via assembly stubs.
/// The caller must ensure the provided address is valid to query.
unsafe fn query_virtual_memory_with_retry(address: *mut c_void) -> Option<MEMORY_BASIC_INFORMATION> {
    trace_log!("Querying virtual memory for address: {:p}", address);
    
    // SAFETY: get_call_r12_gadgets and go_go_gadget are unsafe library functions.
    let gadgets = get_call_r12_gadgets();
    if gadgets.is_empty() { return None; }
    let gadget = go_go_gadget(gadgets);
    if gadget.is_null() { return None; }

    for &syscall_name in &[ZW_QUERY_VIRTUAL_MEMORY, NT_QUERY_VIRTUAL_MEMORY] {
        if let Some(syscall) = get_syscall(syscall_name) {
            DW_SSN = syscall.ssn;
            QW_JMP = syscall.syscall;

            let mut mbi: MEMORY_BASIC_INFORMATION = std::mem::zeroed();
            let mut return_length: usize = 0;
            
            // SAFETY: CallR12 executes code via an assembly stub. We are providing
            // arguments consistent with the NtQueryVirtualMemory signature.
            let result = CallR12(
                CallMe as *mut c_void,
                6, gadget,
                nt_current_process(),
                address,
                0 as *mut c_void, // MemoryInformationClass = MemoryBasicInformation
                &mut mbi as *mut _ as *mut c_void,
                size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
                &mut return_length as *mut _ as *mut c_void,
            ) as usize;

            if result == 0 { // STATUS_SUCCESS
                info_log!("NtQueryVirtualMemory success for {:p} using '{}'", address, syscall_name);
                return Some(mbi);
            }
            warn_log!("NtQueryVirtualMemory failed using '{}' for {:p}, status: {:#x}", syscall_name, address, result);
        }
    }
    error_log!("All NtQueryVirtualMemory attempts failed for address {:p}", address);
    None
}

/// [New] Gets all necessary function info from PE unwind data in one go.
/// # SAFETY
/// This function is unsafe because it calls multiple unsafe library functions
/// to parse PE structures from arbitrary memory locations.
unsafe fn get_function_info(function_address: *mut c_void) -> Option<FunctionInfo> {
    trace_log!("Getting function info for address: {:p}", function_address);
    if function_address.is_null() { return None; }

    // SAFETY: These are all unsafe library functions.
    let gadgets = get_call_r12_gadgets();
    if gadgets.is_empty() { return None; }
    let gadget = go_go_gadget(gadgets);
    if gadget.is_null() { return None; }
    let ntdll = get_ntdll();
    if ntdll.is_null() { return None; }
    
    const RTL_LOOKUP_NAME: &[u8] = b"RtlLookupFunctionEntry\0";
    let rtl_lookup_addr = get_function_address(ntdll, RTL_LOOKUP_NAME);
    if rtl_lookup_addr.is_null() {
        error_log!("Could not find address of RtlLookupFunctionEntry in ntdll.dll.");
        return None;
    }

    let mut image_base: u64 = 0;
    // SAFETY: Calling RtlLookupFunctionEntry via our assembly stub.
    let runtime_function = CallR12(
        rtl_lookup_addr,
        3, gadget,
        function_address as u64 as *mut c_void,
        &mut image_base as *mut _ as *mut c_void,
        null_mut::<c_void>(),
    ) as *const RUNTIME_FUNCTION;

    if runtime_function.is_null() || image_base == 0 {
        warn_log!("RtlLookupFunctionEntry failed for {:p}. The function may not have unwind info.", function_address);
        return None;
    }

    // SAFETY: We are dereferencing pointers based on offsets from `image_base` and
    // the `RUNTIME_FUNCTION` structure, which we assume are valid after the successful API call.
    let unwind_info = (image_base + (*runtime_function).UnwindInfoAddress as u64) as *const UNWIND_INFO;
    let size_of_prolog = (*unwind_info).SizeOfProlog as usize;
    let function_start = (image_base + (*runtime_function).BeginAddress as u64) as *mut c_void;
    let function_end = (image_base + (*runtime_function).EndAddress as u64) as *mut c_void;

    info_log!("PE Unwind Info: start={:p}, end={:p}, prologue_size={}", function_start, function_end, size_of_prolog);

    Some(FunctionInfo {
        image_base: image_base as *mut c_void,
        function_start,
        function_end,
        prologue_end: function_start.add(size_of_prolog),
        total_size: function_end as usize - function_start as usize,
    })
}

/// [Enhanced] Checks if an address is in a valid, executable section of a module.
/// # SAFETY
/// This function is unsafe because it reads from arbitrary memory locations to parse
/// PE header data. `module_base` must point to the start of a valid PE module.
unsafe fn is_valid_executable_address(address: *mut c_void, module_base: *mut c_void) -> bool {
    trace_log!("Validating executable address: {:p} within module: {:p}", address, module_base);
    if address.is_null() || module_base.is_null() { return false; }

    // 1. Fast check: memory page protection.
    if let Some(mbi) = query_virtual_memory_with_retry(address) {
        if (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) == 0 {
            warn_log!("Address {:p} is not in an executable page (Protect: {:#x})", address, mbi.Protect);
            return false;
        }
    } else {
        error_log!("Could not query memory info for address {:p}", address);
        return false;
    }

    // 2. Precise check: PE section headers.
    // SAFETY: Reading PE headers from `module_base`.
    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != 0x5A4D { return false; }
    let nt_headers = (module_base as *const u8).add((*dos_header).e_lfanew as usize) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != 0x4550 { return false; }

    let sections = (&(*nt_headers).OptionalHeader as *const _ as *const u8)
        .add(size_of::<IMAGE_OPTIONAL_HEADER64>()) as *const IMAGE_SECTION_HEADER;
    let number_of_sections = (*nt_headers).FileHeader.NumberOfSections;
    let addr_offset = address as usize - module_base as usize;

    for i in 0..number_of_sections {
        let section = &*sections.add(i as usize);
        let section_start = section.VirtualAddress as usize;
        let section_end = section_start + section.Misc.VirtualSize as usize;
        if addr_offset >= section_start && addr_offset < section_end {
            let is_executable = (section.Characteristics & IMAGE_SCN_MEM_EXECUTE) != 0;
            if is_executable {
                info_log!("Address {:p} confirmed to be in executable PE section.", address);
                return true;
            }
            warn_log!("Address {:p} is in a non-executable PE section.", address);
            return false;
        }
    }

    warn_log!("Address {:p} was not found in any PE section of module {:p}.", address, module_base);
    false
}

/// Finds a `ret` instruction using the HDE64 disassembler.
/// # SAFETY
/// This function is unsafe because it reads and interprets raw bytes from `code` as
/// machine instructions. The caller must ensure `code` is a valid pointer to code.
pub unsafe fn find_return_instruction(code: *const u8, max_length: usize) -> Option<*mut c_void> {
    trace_log!("Scanning for return instruction at {:p}, length {}", code, max_length);
    if code.is_null() || max_length == 0 { return None; }

    let mut offset = 0;
    while offset < max_length {
        let current_ptr = code.add(offset);
        let mut hs = HDE64::default();
        let len = hde64_disasm(current_ptr, &mut hs, max_length - offset) as usize;

        if len == 0 { break; } // Disassembly error

        // Check for RET opcodes (C3, C2, CB, CA)
        if matches!(hs.opcode, 0xC2 | 0xC3 | 0xCA | 0xCB) {
            info_log!("Found return instruction (opcode {:#x}) at {:p}", hs.opcode, current_ptr);
            return Some(current_ptr as *mut c_void);
        }
        offset += len;
    }
    dbg_log!("No return instruction found in the given range.");
    None
}

/// [Main Function] Finds the return address of a function.
/// # SAFETY
/// This function is the main entry point and orchestrates multiple unsafe operations.
/// The caller must provide a valid pointer to the beginning of a function.
pub unsafe fn find_return_address(function_address: *mut c_void) -> *mut c_void {
    trace_log!("Starting return address search for function: {:p}", function_address);
    if function_address.is_null() { return null_mut(); }

    // 1. Try to use PE unwind info for an intelligent scan.
    if let Some(info) = get_function_info(function_address) {
        info_log!("Method 1: Scanning after prologue using unwind info.");
        let scan_start = info.prologue_end as *const u8;
        let scan_len = info.function_end as usize - info.prologue_end as usize;
        if scan_len > 0 {
            if let Some(ret_addr) = find_return_instruction(scan_start, scan_len) {
                if is_valid_executable_address(ret_addr, info.image_base) {
                    info_log!("✓ Success (Method 1): Found valid return address: {:p}", ret_addr);
                    return ret_addr;
                }
            }
        }

        info_log!("Method 2: Scanning entire function body using unwind info.");
        if let Some(ret_addr) = find_return_instruction(info.function_start as *const u8, info.total_size) {
             if is_valid_executable_address(ret_addr, info.image_base) {
                info_log!("✓ Success (Method 2): Found valid return address: {:p}", ret_addr);
                return ret_addr;
            }
        }
    }

    // 2. Fallback to a brute-force scan if unwind info fails.
    warn_log!("Unwind info failed or yielded no result. Falling back to brute-force scan.");
    const FALLBACK_SCAN_SIZE: usize = 5000;
    if let Some(ret_addr) = find_return_instruction(function_address as *const u8, FALLBACK_SCAN_SIZE) {
        // We need to find the module base to validate the address
        if let Some(mbi) = query_virtual_memory_with_retry(ret_addr) {
             if is_valid_executable_address(ret_addr, mbi.AllocationBase) {
                info_log!("✓ Success (Fallback): Found valid return address: {:p}", ret_addr);
                return ret_addr;
            }
        }
    }

    error_log!("All methods failed for function {:p}. Could not find a return address.", function_address);
    null_mut()
}