// logger.rs

use std::ffi::c_void;
use std::fs::{File, OpenOptions};
use std::io::{self, Write};
use std::time::{SystemTime, UNIX_EPOCH};

/// A simple logger for writing timestamped messages to the console and a file.
pub struct Logger {
    pub log_file: Option<File>,
    /// The logging verbosity.
    /// 0=Off, 1=Error, 2=Warn, 3=Info, 4=Debug, 5=Trace.
    pub debug_level: u8,
}

impl Logger {
    /// Initializes the logger, creating or truncating the specified log file.
    /// If file creation fails, an error is printed to the console.
    pub fn init(&mut self, filename: &str) {
        match OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(filename)
        {
            Ok(file) => {
                self.log_file = Some(file);
                self.info("Logger initialized successfully.");
            }
            Err(e) => {
                // Use eprintln for errors, which prints to stderr.
                eprintln!("Failed to open log file '{}': {}", filename, e);
                self.log_file = None;
            }
        }
    }

    /// Logs a message if its level is at or below the current `debug_level`.
    ///
    /// This is the core logging function. For performance, it checks the
    /// log level before performing any string formatting.
    ///
    /// # Arguments
    /// * `message` - The message to log.
    /// * `level` - The severity level of the message.
    pub fn log(&mut self, message: &str, level: u8) {
        // Performance: Check level first to avoid expensive work for disabled logs.
        if level > self.debug_level {
            return;
        }

        let level_prefix = match level {
            1 => "[ERROR]",
            2 => "[WARN] ",
            3 => "[INFO] ",
            4 => "[DEBUG]",
            5 => "[TRACE]",
            _ => "[UNKWN]",
        };

        // This can only fail if the system clock is set before the UNIX epoch.
        // `unwrap_or_default` handles this gracefully.
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();
            
        let log_message = format!("[{:.6}] {} {}\n", timestamp, level_prefix, message);

        // Print to console.
        print!("{}", log_message);

        // Write to file if available.
        if let Some(file) = &mut self.log_file {
            // Errors during logging are often ignored, but we can report them.
            if let Err(e) = file.write_all(log_message.as_bytes()) {
                 eprintln!("Error writing to log file: {}", e);
            }
        }
    }
    
    /// Logs an error message (level 1).
    pub fn error(&mut self, message: &str) {
        self.log(message, 1);
    }

    /// Logs a warning message (level 2).
    pub fn warn(&mut self, message: &str) {
        self.log(message, 2);
    }

    /// Logs an info message (level 3).
    pub fn info(&mut self, message: &str) {
        self.log(message, 3);
    }

    /// Logs a debug message (level 4).
    pub fn debug(&mut self, message: &str) {
        self.log(message, 4);
    }

    /// Logs a trace message (level 5).
    pub fn trace(&mut self, message: &str) {
        self.log(message, 5);
    }

    /// Returns the current time as a high-precision floating point number.
    fn get_timestamp(&self) -> f64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64()
    }
    
    /// Logs the start of an operation and returns its start time for measuring duration.
    pub fn start_operation(&mut self, operation_name: &str) -> f64 {
        let timestamp = self.get_timestamp();
        self.info(&format!(
            "START OPERATION: {} (timestamp: {:.6})",
            operation_name, timestamp
        ));
        timestamp
    }

    /// Logs the end of an operation and its total duration.
    pub fn end_operation(&mut self, operation_name: &str, start_timestamp: f64) {
        let end_timestamp = self.get_timestamp();
        let duration = end_timestamp - start_timestamp;
        self.info(&format!(
            "END OPERATION: {} (duration: {:.6} seconds)",
            operation_name, duration
        ));
    }

    /// Logs a memory address with a descriptive name.
    pub fn log_address(&mut self, name: &str, address: *const c_void) {
        self.debug(&format!(
            "MEMORY ADDRESS: {} at {:p} (decimal: {})",
            name, address, address as usize
        ));
    }

    /// Logs a function call with its parameters.
    pub fn log_function_call(&mut self, function_name: &str, params: &[(&str, String)]) {
        let params_str = params
            .iter()
            .map(|(name, value)| format!("{}: {}", name, value))
            .collect::<Vec<String>>()
            .join(", ");
            
        self.trace(&format!("FUNCTION CALL: {}({})", function_name, params_str));
    }
    
    /// Logs the current stack pointer (RSP).
    /// This version is simplified to avoid logging large amounts of data,
    /// which can be crucial in preventing stack overflows in highly recursive code.
    pub fn log_stack_state(&mut self, prefix: &str) {
        let rsp: usize;
        // SAFETY: Reading the stack pointer register (rsp) is a read-only operation
        // that doesn't violate memory safety. However, `asm!` is unsafe because
        // the compiler cannot verify the assembly code's behavior.
        unsafe {
            std::arch::asm!("mov {}, rsp", out(reg) rsp, options(nostack, nomem));
        }

        self.trace(&format!("STACK STATE {}: RSP = 0x{:016X}", prefix, rsp));
    }

    /// Logs the current stack pointer (RSP) and instruction pointer (RIP).
    /// This is simplified to prevent stack overflows during frequent logging.
    pub fn log_register_state(&mut self, prefix: &str) {
        let rsp: usize;
        let rip: usize;

        // SAFETY: Reading CPU registers is inherently unsafe from the compiler's
        // perspective. We are only reading values, which is safe in practice,
        // but we must still use an `unsafe` block.
        unsafe {
            std::arch::asm!(
                "mov {rsp}, rsp",
                "lea {rip}, [rip]", // Get the address of the next instruction
                rsp = out(reg) rsp,
                rip = out(reg) rip,
                options(nostack, nomem)
            );
        }

        self.trace(&format!("REGISTER STATE {}: RSP=0x{:016X}, RIP=0x{:016X}", prefix, rsp, rip));
    }
}