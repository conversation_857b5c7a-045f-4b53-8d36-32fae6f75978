{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\logger.rs","byte_start":88,"byte_end":92,"line_start":5,"line_end":5,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\logger.rs","byte_start":88,"byte_end":94,"line_start":5,"line_end":5,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\logger.rs","byte_start":87,"byte_end":88,"line_start":5,"line_end":5,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\logger.rs","byte_start":99,"byte_end":100,"line_start":5,"line_end":5,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `self`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\logger.rs:5:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{self, Write};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Machine` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1652,"byte_end":1659,"line_start":49,"line_end":49,"column_start":47,"column_end":54,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":47,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1652,"byte_end":1659,"line_start":49,"line_end":49,"column_start":47,"column_end":54,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":47,"highlight_end":54}],"label":null,"suggested_replacement":"machine","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Machine` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16,\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `machine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfSections` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1670,"byte_end":1686,"line_start":49,"line_end":49,"column_start":65,"column_end":81,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":65,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1670,"byte_end":1686,"line_start":49,"line_end":49,"column_start":65,"column_end":81,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":65,"highlight_end":81}],"label":null,"suggested_replacement":"number_of_sections","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfSections` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:65\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16,\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_sections`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `TimeDateStamp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1697,"byte_end":1710,"line_start":49,"line_end":49,"column_start":92,"column_end":105,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":92,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1697,"byte_end":1710,"line_start":49,"line_end":49,"column_start":92,"column_end":105,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":92,"highlight_end":105}],"label":null,"suggested_replacement":"time_date_stamp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `TimeDateStamp` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:92\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16,\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `time_date_stamp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToSymbolTable` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1721,"byte_end":1741,"line_start":49,"line_end":49,"column_start":116,"column_end":136,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":116,"highlight_end":136}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1721,"byte_end":1741,"line_start":49,"line_end":49,"column_start":116,"column_end":136,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":116,"highlight_end":136}],"label":null,"suggested_replacement":"pointer_to_symbol_table","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToSymbolTable` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:116\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_symbol_table`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfSymbols` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1752,"byte_end":1767,"line_start":49,"line_end":49,"column_start":147,"column_end":162,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":147,"highlight_end":162}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1752,"byte_end":1767,"line_start":49,"line_end":49,"column_start":147,"column_end":162,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":147,"highlight_end":162}],"label":null,"suggested_replacement":"number_of_symbols","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfSymbols` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:147\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_symbols`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfOptionalHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1778,"byte_end":1798,"line_start":49,"line_end":49,"column_start":173,"column_end":193,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":173,"highlight_end":193}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1778,"byte_end":1798,"line_start":49,"line_end":49,"column_start":173,"column_end":193,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":173,"highlight_end":193}],"label":null,"suggested_replacement":"size_of_optional_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfOptionalHeader` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:173\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0merToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_optional_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1809,"byte_end":1824,"line_start":49,"line_end":49,"column_start":204,"column_end":219,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":204,"highlight_end":219}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1809,"byte_end":1824,"line_start":49,"line_end":49,"column_start":204,"column_end":219,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_FILE_HEADER { pub Machine: u16, pub NumberOfSections: u16, pub TimeDateStamp: u32, pub PointerToSymbolTable: u32, pub NumberOfSymbols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }","highlight_start":204,"highlight_end":219}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:204\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mols: u32, pub SizeOfOptionalHeader: u16, pub Characteristics: u16, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1882,"byte_end":1896,"line_start":50,"line_end":50,"column_start":50,"column_end":64,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }","highlight_start":50,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1882,"byte_end":1896,"line_start":50,"line_end":50,"column_start":50,"column_end":64,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }","highlight_start":50,"highlight_end":64}],"label":null,"suggested_replacement":"virtual_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:50:50\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Size` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1907,"byte_end":1911,"line_start":50,"line_end":50,"column_start":75,"column_end":79,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }","highlight_start":75,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1907,"byte_end":1911,"line_start":50,"line_end":50,"column_start":75,"column_end":79,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }","highlight_start":75,"highlight_end":79}],"label":null,"suggested_replacement":"size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Size` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:50:75\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_DATA_DIRECTORY { pub VirtualAddress: u32, pub Size: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Magic` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1972,"byte_end":1977,"line_start":51,"line_end":51,"column_start":53,"column_end":58,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":53,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1972,"byte_end":1977,"line_start":51,"line_end":51,"column_start":53,"column_end":58,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":53,"highlight_end":58}],"label":null,"suggested_replacement":"magic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Magic` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedDa\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `magic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorLinkerVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1988,"byte_end":2006,"line_start":51,"line_end":51,"column_start":69,"column_end":87,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":69,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1988,"byte_end":2006,"line_start":51,"line_end":51,"column_start":69,"column_end":87,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":69,"highlight_end":87}],"label":null,"suggested_replacement":"major_linker_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorLinkerVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedDa\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_linker_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorLinkerVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2016,"byte_end":2034,"line_start":51,"line_end":51,"column_start":97,"column_end":115,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":97,"highlight_end":115}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2016,"byte_end":2034,"line_start":51,"line_end":51,"column_start":97,"column_end":115,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":97,"highlight_end":115}],"label":null,"suggested_replacement":"minor_linker_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorLinkerVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:97\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedDa\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_linker_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfCode` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2044,"byte_end":2054,"line_start":51,"line_end":51,"column_start":125,"column_end":135,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":125,"highlight_end":135}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2044,"byte_end":2054,"line_start":51,"line_end":51,"column_start":125,"column_end":135,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":125,"highlight_end":135}],"label":null,"suggested_replacement":"size_of_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfCode` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:125\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedDa\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfInitializedData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2065,"byte_end":2086,"line_start":51,"line_end":51,"column_start":146,"column_end":167,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":146,"highlight_end":167}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2065,"byte_end":2086,"line_start":51,"line_end":51,"column_start":146,"column_end":167,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":146,"highlight_end":167}],"label":null,"suggested_replacement":"size_of_initialized_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfInitializedData` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:146\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mb MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub Section\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_initialized_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfUninitializedData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2097,"byte_end":2120,"line_start":51,"line_end":51,"column_start":178,"column_end":201,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":178,"highlight_end":201}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2097,"byte_end":2120,"line_start":51,"line_end":51,"column_start":178,"column_end":201,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":178,"highlight_end":201}],"label":null,"suggested_replacement":"size_of_uninitialized_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfUninitializedData` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:178\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment:\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_uninitialized_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfEntryPoint` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2131,"byte_end":2150,"line_start":51,"line_end":51,"column_start":212,"column_end":231,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":212,"highlight_end":231}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2131,"byte_end":2150,"line_start":51,"line_end":51,"column_start":212,"column_end":231,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":212,"highlight_end":231}],"label":null,"suggested_replacement":"address_of_entry_point","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfEntryPoint` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:212\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mizedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystem\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_entry_point`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `BaseOfCode` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2161,"byte_end":2171,"line_start":51,"line_end":51,"column_start":242,"column_end":252,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":242,"highlight_end":252}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2161,"byte_end":2171,"line_start":51,"line_end":51,"column_start":242,"column_end":252,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":242,"highlight_end":252}],"label":null,"suggested_replacement":"base_of_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `BaseOfCode` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:242\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub Mi\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `base_of_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `ImageBase` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2182,"byte_end":2191,"line_start":51,"line_end":51,"column_start":263,"column_end":272,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":263,"highlight_end":272}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2182,"byte_end":2191,"line_start":51,"line_end":51,"column_start":263,"column_end":272,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":263,"highlight_end":272}],"label":null,"suggested_replacement":"image_base","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `ImageBase` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:263\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mta: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVe\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `image_base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SectionAlignment` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2202,"byte_end":2218,"line_start":51,"line_end":51,"column_start":283,"column_end":299,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":283,"highlight_end":299}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2202,"byte_end":2218,"line_start":51,"line_end":51,"column_start":283,"column_end":299,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":283,"highlight_end":299}],"label":null,"suggested_replacement":"section_alignment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SectionAlignment` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:283\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageV\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `section_alignment`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FileAlignment` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2229,"byte_end":2242,"line_start":51,"line_end":51,"column_start":310,"column_end":323,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":310,"highlight_end":323}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2229,"byte_end":2242,"line_start":51,"line_end":51,"column_start":310,"column_end":323,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":310,"highlight_end":323}],"label":null,"suggested_replacement":"file_alignment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FileAlignment` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:310\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mde: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorIm\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `file_alignment`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorOperatingSystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2253,"byte_end":2280,"line_start":51,"line_end":51,"column_start":334,"column_end":361,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":334,"highlight_end":361}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2253,"byte_end":2280,"line_start":51,"line_end":51,"column_start":334,"column_end":361,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":334,"highlight_end":361}],"label":null,"suggested_replacement":"major_operating_system_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorOperatingSystemVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:334\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mnAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVers\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_operating_system_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorOperatingSystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2291,"byte_end":2318,"line_start":51,"line_end":51,"column_start":372,"column_end":399,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":372,"highlight_end":399}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2291,"byte_end":2318,"line_start":51,"line_end":51,"column_start":372,"column_end":399,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":372,"highlight_end":399}],"label":null,"suggested_replacement":"minor_operating_system_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorOperatingSystemVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:372\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m2, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_operating_system_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorImageVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2329,"byte_end":2346,"line_start":51,"line_end":51,"column_start":410,"column_end":427,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":410,"highlight_end":427}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2329,"byte_end":2346,"line_start":51,"line_end":51,"column_start":410,"column_end":427,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":410,"highlight_end":427}],"label":null,"suggested_replacement":"major_image_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorImageVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:410\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_image_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorImageVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2357,"byte_end":2374,"line_start":51,"line_end":51,"column_start":438,"column_end":455,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":438,"highlight_end":455}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2357,"byte_end":2374,"line_start":51,"line_end":51,"column_start":438,"column_end":455,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":438,"highlight_end":455}],"label":null,"suggested_replacement":"minor_image_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorImageVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:438\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mtingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, p\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_image_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorSubsystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2385,"byte_end":2406,"line_start":51,"line_end":51,"column_start":466,"column_end":487,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":466,"highlight_end":487}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2385,"byte_end":2406,"line_start":51,"line_end":51,"column_start":466,"column_end":487,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":466,"highlight_end":487}],"label":null,"suggested_replacement":"major_subsystem_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorSubsystemVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:466\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mrImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub Check\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_subsystem_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorSubsystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2417,"byte_end":2438,"line_start":51,"line_end":51,"column_start":498,"column_end":519,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":498,"highlight_end":519}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2417,"byte_end":2438,"line_start":51,"line_end":51,"column_start":498,"column_end":519,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":498,"highlight_end":519}],"label":null,"suggested_replacement":"minor_subsystem_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorSubsystemVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:498\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mgeVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pu\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_subsystem_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Win32VersionValue` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2449,"byte_end":2466,"line_start":51,"line_end":51,"column_start":530,"column_end":547,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":530,"highlight_end":547}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2449,"byte_end":2466,"line_start":51,"line_end":51,"column_start":530,"column_end":547,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":530,"highlight_end":547}],"label":null,"suggested_replacement":"win32_version_value","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Win32VersionValue` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:530\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0msystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, p\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `win32_version_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfImage` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2477,"byte_end":2488,"line_start":51,"line_end":51,"column_start":558,"column_end":569,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":558,"highlight_end":569}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2477,"byte_end":2488,"line_start":51,"line_end":51,"column_start":558,"column_end":569,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":558,"highlight_end":569}],"label":null,"suggested_replacement":"size_of_image","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfImage` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:558\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mb MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve:\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_image`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeaders` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2499,"byte_end":2512,"line_start":51,"line_end":51,"column_start":580,"column_end":593,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":580,"highlight_end":593}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2499,"byte_end":2512,"line_start":51,"line_end":51,"column_start":580,"column_end":593,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":580,"highlight_end":593}],"label":null,"suggested_replacement":"size_of_headers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeaders` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:580\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCom\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_headers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `CheckSum` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2523,"byte_end":2531,"line_start":51,"line_end":51,"column_start":604,"column_end":612,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":604,"highlight_end":612}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2523,"byte_end":2531,"line_start":51,"line_end":51,"column_start":604,"column_end":612,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":604,"highlight_end":612}],"label":null,"suggested_replacement":"check_sum","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `CheckSum` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:604\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeO\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `check_sum`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Subsystem` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2542,"byte_end":2551,"line_start":51,"line_end":51,"column_start":623,"column_end":632,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":623,"highlight_end":632}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2542,"byte_end":2551,"line_start":51,"line_end":51,"column_start":623,"column_end":632,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":623,"highlight_end":632}],"label":null,"suggested_replacement":"subsystem","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Subsystem` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:623\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeap\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `subsystem`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `DllCharacteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2562,"byte_end":2580,"line_start":51,"line_end":51,"column_start":643,"column_end":661,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":643,"highlight_end":661}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2562,"byte_end":2580,"line_start":51,"line_end":51,"column_start":643,"column_end":661,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":643,"highlight_end":661}],"label":null,"suggested_replacement":"dll_characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `DllCharacteristics` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:643\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0maders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `dll_characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfStackReserve` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2591,"byte_end":2609,"line_start":51,"line_end":51,"column_start":672,"column_end":690,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":672,"highlight_end":690}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2591,"byte_end":2609,"line_start":51,"line_end":51,"column_start":672,"column_end":690,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":672,"highlight_end":690}],"label":null,"suggested_replacement":"size_of_stack_reserve","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfStackReserve` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:672\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberO\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_stack_reserve`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfStackCommit` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2620,"byte_end":2637,"line_start":51,"line_end":51,"column_start":701,"column_end":718,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":701,"highlight_end":718}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2620,"byte_end":2637,"line_start":51,"line_end":51,"column_start":701,"column_end":718,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":701,"highlight_end":718}],"label":null,"suggested_replacement":"size_of_stack_commit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfStackCommit` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:701\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataD\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_stack_commit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeapReserve` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2648,"byte_end":2665,"line_start":51,"line_end":51,"column_start":729,"column_end":746,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":729,"highlight_end":746}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2648,"byte_end":2665,"line_start":51,"line_end":51,"column_start":729,"column_end":746,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":729,"highlight_end":746}],"label":null,"suggested_replacement":"size_of_heap_reserve","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeapReserve` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:729\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mzeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECT\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_heap_reserve`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeapCommit` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2676,"byte_end":2692,"line_start":51,"line_end":51,"column_start":757,"column_end":773,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":757,"highlight_end":773}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2676,"byte_end":2692,"line_start":51,"line_end":51,"column_start":757,"column_end":773,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":757,"highlight_end":773}],"label":null,"suggested_replacement":"size_of_heap_commit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeapCommit` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:757\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mSizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_heap_commit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `LoaderFlags` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2703,"byte_end":2714,"line_start":51,"line_end":51,"column_start":784,"column_end":795,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":784,"highlight_end":795}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2703,"byte_end":2714,"line_start":51,"line_end":51,"column_start":784,"column_end":795,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":784,"highlight_end":795}],"label":null,"suggested_replacement":"loader_flags","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `LoaderFlags` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:784\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m4, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `loader_flags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfRvaAndSizes` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2725,"byte_end":2744,"line_start":51,"line_end":51,"column_start":806,"column_end":825,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":806,"highlight_end":825}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2725,"byte_end":2744,"line_start":51,"line_end":51,"column_start":806,"column_end":825,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":806,"highlight_end":825}],"label":null,"suggested_replacement":"number_of_rva_and_sizes","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfRvaAndSizes` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:806\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mpub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_rva_and_sizes`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `DataDirectory` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2755,"byte_end":2768,"line_start":51,"line_end":51,"column_start":836,"column_end":849,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":836,"highlight_end":849}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2755,"byte_end":2768,"line_start":51,"line_end":51,"column_start":836,"column_end":849,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_OPTIONAL_HEADER64 { pub Magic: u16, pub MajorLinkerVersion: u8, pub MinorLinkerVersion: u8, pub SizeOfCode: u32, pub SizeOfInitializedData: u32, pub SizeOfUninitializedData: u32, pub AddressOfEntryPoint: u32, pub BaseOfCode: u32, pub ImageBase: u64, pub SectionAlignment: u32, pub FileAlignment: u32, pub MajorOperatingSystemVersion: u16, pub MinorOperatingSystemVersion: u16, pub MajorImageVersion: u16, pub MinorImageVersion: u16, pub MajorSubsystemVersion: u16, pub MinorSubsystemVersion: u16, pub Win32VersionValue: u32, pub SizeOfImage: u32, pub SizeOfHeaders: u32, pub CheckSum: u32, pub Subsystem: u16, pub DllCharacteristics: u16, pub SizeOfStackReserve: u64, pub SizeOfStackCommit: u64, pub SizeOfHeapReserve: u64, pub SizeOfHeapCommit: u64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }","highlight_start":836,"highlight_end":849}],"label":null,"suggested_replacement":"data_directory","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `DataDirectory` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:51:836\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m64, pub LoaderFlags: u32, pub NumberOfRvaAndSizes: u32, pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16], }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `data_directory`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Signature` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2847,"byte_end":2856,"line_start":52,"line_end":52,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2847,"byte_end":2856,"line_start":52,"line_end":52,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":"signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Signature` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:52:48\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `signature`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FileHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2867,"byte_end":2877,"line_start":52,"line_end":52,"column_start":68,"column_end":78,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":68,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2867,"byte_end":2877,"line_start":52,"line_end":52,"column_start":68,"column_end":78,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":68,"highlight_end":78}],"label":null,"suggested_replacement":"file_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FileHeader` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:52:68\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `file_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `OptionalHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2902,"byte_end":2916,"line_start":52,"line_end":52,"column_start":103,"column_end":117,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":103,"highlight_end":117}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2902,"byte_end":2916,"line_start":52,"line_end":52,"column_start":103,"column_end":117,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }","highlight_start":103,"highlight_end":117}],"label":null,"suggested_replacement":"optional_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `OptionalHeader` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:52:103\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_NT_HEADERS64 { pub Signature: u32, pub FileHeader: IMAGE_FILE_HEADER, pub OptionalHeader: IMAGE_OPTIONAL_HEADER64, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `optional_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2996,"byte_end":3011,"line_start":53,"line_end":53,"column_start":52,"column_end":67,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":52,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2996,"byte_end":3011,"line_start":53,"line_end":53,"column_start":52,"column_end":67,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":52,"highlight_end":67}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u3\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `TimeDateStamp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3022,"byte_end":3035,"line_start":53,"line_end":53,"column_start":78,"column_end":91,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":78,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3022,"byte_end":3035,"line_start":53,"line_end":53,"column_start":78,"column_end":91,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":78,"highlight_end":91}],"label":null,"suggested_replacement":"time_date_stamp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `TimeDateStamp` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:78\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u3\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `time_date_stamp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3046,"byte_end":3058,"line_start":53,"line_end":53,"column_start":102,"column_end":114,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":102,"highlight_end":114}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3046,"byte_end":3058,"line_start":53,"line_end":53,"column_start":102,"column_end":114,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":102,"highlight_end":114}],"label":null,"suggested_replacement":"major_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:102\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u3\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3069,"byte_end":3081,"line_start":53,"line_end":53,"column_start":125,"column_end":137,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":125,"highlight_end":137}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3069,"byte_end":3081,"line_start":53,"line_end":53,"column_start":125,"column_end":137,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":125,"highlight_end":137}],"label":null,"suggested_replacement":"minor_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorVersion` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:125\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3092,"byte_end":3096,"line_start":53,"line_end":53,"column_start":148,"column_end":152,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":148,"highlight_end":152}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3092,"byte_end":3096,"line_start":53,"line_end":53,"column_start":148,"column_end":152,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":148,"highlight_end":152}],"label":null,"suggested_replacement":"name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Name` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:148\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mTimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Base` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3107,"byte_end":3111,"line_start":53,"line_end":53,"column_start":163,"column_end":167,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":163,"highlight_end":167}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3107,"byte_end":3111,"line_start":53,"line_end":53,"column_start":163,"column_end":167,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":163,"highlight_end":167}],"label":null,"suggested_replacement":"base","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Base` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:163\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mu32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub Addr\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfFunctions` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3122,"byte_end":3139,"line_start":53,"line_end":53,"column_start":178,"column_end":195,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":178,"highlight_end":195}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3122,"byte_end":3139,"line_start":53,"line_end":53,"column_start":178,"column_end":195,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":178,"highlight_end":195}],"label":null,"suggested_replacement":"number_of_functions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfFunctions` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:178\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_functions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfNames` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3150,"byte_end":3163,"line_start":53,"line_end":53,"column_start":206,"column_end":219,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":206,"highlight_end":219}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3150,"byte_end":3163,"line_start":53,"line_end":53,"column_start":206,"column_end":219,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":206,"highlight_end":219}],"label":null,"suggested_replacement":"number_of_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfNames` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:206\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mb Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfFunctions` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3174,"byte_end":3192,"line_start":53,"line_end":53,"column_start":230,"column_end":248,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":230,"highlight_end":248}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3174,"byte_end":3192,"line_start":53,"line_end":53,"column_start":230,"column_end":248,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":230,"highlight_end":248}],"label":null,"suggested_replacement":"address_of_functions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfFunctions` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:230\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_functions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfNames` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3203,"byte_end":3217,"line_start":53,"line_end":53,"column_start":259,"column_end":273,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":259,"highlight_end":273}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3203,"byte_end":3217,"line_start":53,"line_end":53,"column_start":259,"column_end":273,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":259,"highlight_end":273}],"label":null,"suggested_replacement":"address_of_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfNames` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:259\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfNameOrdinals` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3228,"byte_end":3249,"line_start":53,"line_end":53,"column_start":284,"column_end":305,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":284,"highlight_end":305}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3228,"byte_end":3249,"line_start":53,"line_end":53,"column_start":284,"column_end":305,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_EXPORT_DIRECTORY { pub Characteristics: u32, pub TimeDateStamp: u32, pub MajorVersion: u16, pub MinorVersion: u16, pub Name: u32, pub Base: u32, pub NumberOfFunctions: u32, pub NumberOfNames: u32, pub AddressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }","highlight_start":284,"highlight_end":305}],"label":null,"suggested_replacement":"address_of_name_ordinals","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfNameOrdinals` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:53:284\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mressOfFunctions: u32, pub AddressOfNames: u32, pub AddressOfNameOrdinals: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_name_ordinals`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3307,"byte_end":3311,"line_start":54,"line_end":54,"column_start":50,"column_end":54,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":50,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3307,"byte_end":3311,"line_start":54,"line_end":54,"column_start":50,"column_end":54,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":50,"highlight_end":54}],"label":null,"suggested_replacement":"name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Name` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:50\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocati\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Misc` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3326,"byte_end":3330,"line_start":54,"line_end":54,"column_start":69,"column_end":73,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":69,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3326,"byte_end":3330,"line_start":54,"line_end":54,"column_start":69,"column_end":73,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":69,"highlight_end":73}],"label":null,"suggested_replacement":"misc","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Misc` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocati\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `misc`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3363,"byte_end":3377,"line_start":54,"line_end":54,"column_start":106,"column_end":120,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":106,"highlight_end":120}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3363,"byte_end":3377,"line_start":54,"line_end":54,"column_start":106,"column_end":120,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":106,"highlight_end":120}],"label":null,"suggested_replacement":"virtual_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:106\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocati\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfRawData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3388,"byte_end":3401,"line_start":54,"line_end":54,"column_start":131,"column_end":144,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":131,"highlight_end":144}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3388,"byte_end":3401,"line_start":54,"line_end":54,"column_start":131,"column_end":144,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":131,"highlight_end":144}],"label":null,"suggested_replacement":"size_of_raw_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfRawData` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:131\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u1\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_raw_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToRawData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3412,"byte_end":3428,"line_start":54,"line_end":54,"column_start":155,"column_end":171,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":155,"highlight_end":171}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3412,"byte_end":3428,"line_start":54,"line_end":54,"column_start":155,"column_end":171,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":155,"highlight_end":171}],"label":null,"suggested_replacement":"pointer_to_raw_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToRawData` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:155\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers:\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_raw_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToRelocations` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3439,"byte_end":3459,"line_start":54,"line_end":54,"column_start":182,"column_end":202,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":182,"highlight_end":202}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3439,"byte_end":3459,"line_start":54,"line_end":54,"column_start":182,"column_end":202,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":182,"highlight_end":202}],"label":null,"suggested_replacement":"pointer_to_relocations","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToRelocations` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:182\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mb SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_relocations`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToLinenumbers` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3470,"byte_end":3490,"line_start":54,"line_end":54,"column_start":213,"column_end":233,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":213,"highlight_end":233}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3470,"byte_end":3490,"line_start":54,"line_end":54,"column_start":213,"column_end":233,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":213,"highlight_end":233}],"label":null,"suggested_replacement":"pointer_to_linenumbers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToLinenumbers` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:213\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0merToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_linenumbers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfRelocations` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3501,"byte_end":3520,"line_start":54,"line_end":54,"column_start":244,"column_end":263,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":244,"highlight_end":263}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3501,"byte_end":3520,"line_start":54,"line_end":54,"column_start":244,"column_end":263,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":244,"highlight_end":263}],"label":null,"suggested_replacement":"number_of_relocations","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfRelocations` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:244\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0moRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_relocations`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfLinenumbers` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3531,"byte_end":3550,"line_start":54,"line_end":54,"column_start":274,"column_end":293,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":274,"highlight_end":293}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3531,"byte_end":3550,"line_start":54,"line_end":54,"column_start":274,"column_end":293,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":274,"highlight_end":293}],"label":null,"suggested_replacement":"number_of_linenumbers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfLinenumbers` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:274\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_linenumbers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3561,"byte_end":3576,"line_start":54,"line_end":54,"column_start":304,"column_end":319,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":304,"highlight_end":319}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3561,"byte_end":3576,"line_start":54,"line_end":54,"column_start":304,"column_end":319,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct IMAGE_SECTION_HEADER { pub Name: [u8; 8], pub Misc: IMAGE_SECTION_HEADER_MISC, pub VirtualAddress: u32, pub SizeOfRawData: u32, pub PointerToRawData: u32, pub PointerToRelocations: u32, pub PointerToLinenumbers: u32, pub NumberOfRelocations: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }","highlight_start":304,"highlight_end":319}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:54:304\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mions: u16, pub NumberOfLinenumbers: u16, pub Characteristics: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PhysicalAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3638,"byte_end":3653,"line_start":55,"line_end":55,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3638,"byte_end":3653,"line_start":55,"line_end":55,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"physical_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PhysicalAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:55:54\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `physical_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualSize` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3664,"byte_end":3675,"line_start":55,"line_end":55,"column_start":80,"column_end":91,"is_primary":true,"text":[{"text":"#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }","highlight_start":80,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3664,"byte_end":3675,"line_start":55,"line_end":55,"column_start":80,"column_end":91,"is_primary":true,"text":[{"text":"#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }","highlight_start":80,"highlight_end":91}],"label":null,"suggested_replacement":"virtual_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualSize` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:55:80\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub union IMAGE_SECTION_HEADER_MISC { pub PhysicalAddress: u32, pub VirtualSize: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `BeginAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3729,"byte_end":3741,"line_start":56,"line_end":56,"column_start":46,"column_end":58,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":46,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3729,"byte_end":3741,"line_start":56,"line_end":56,"column_start":46,"column_end":58,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":46,"highlight_end":58}],"label":null,"suggested_replacement":"begin_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `BeginAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:56:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `begin_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `EndAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3752,"byte_end":3762,"line_start":56,"line_end":56,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3752,"byte_end":3762,"line_start":56,"line_end":56,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":"end_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `EndAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:56:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `end_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `UnwindInfoAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3773,"byte_end":3790,"line_start":56,"line_end":56,"column_start":90,"column_end":107,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":90,"highlight_end":107}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3773,"byte_end":3790,"line_start":56,"line_end":56,"column_start":90,"column_end":107,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }","highlight_start":90,"highlight_end":107}],"label":null,"suggested_replacement":"unwind_info_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `UnwindInfoAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:56:90\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct RUNTIME_FUNCTION { pub BeginAddress: u32, pub EndAddress: u32, pub UnwindInfoAddress: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `unwind_info_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Version` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3839,"byte_end":3846,"line_start":57,"line_end":57,"column_start":41,"column_end":48,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":41,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3839,"byte_end":3846,"line_start":57,"line_end":57,"column_start":41,"column_end":48,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":41,"highlight_end":48}],"label":null,"suggested_replacement":"version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Version` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Flags` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3856,"byte_end":3861,"line_start":57,"line_end":57,"column_start":58,"column_end":63,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":58,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3856,"byte_end":3861,"line_start":57,"line_end":57,"column_start":58,"column_end":63,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":58,"highlight_end":63}],"label":null,"suggested_replacement":"flags","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Flags` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `flags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfProlog` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3871,"byte_end":3883,"line_start":57,"line_end":57,"column_start":73,"column_end":85,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":73,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3871,"byte_end":3883,"line_start":57,"line_end":57,"column_start":73,"column_end":85,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":73,"highlight_end":85}],"label":null,"suggested_replacement":"size_of_prolog","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfProlog` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:73\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_prolog`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `CountOfCodes` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3893,"byte_end":3905,"line_start":57,"line_end":57,"column_start":95,"column_end":107,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":95,"highlight_end":107}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3893,"byte_end":3905,"line_start":57,"line_end":57,"column_start":95,"column_end":107,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":95,"highlight_end":107}],"label":null,"suggested_replacement":"count_of_codes","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `CountOfCodes` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:95\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `count_of_codes`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FrameRegister` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3915,"byte_end":3928,"line_start":57,"line_end":57,"column_start":117,"column_end":130,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":117,"highlight_end":130}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3915,"byte_end":3928,"line_start":57,"line_end":57,"column_start":117,"column_end":130,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":117,"highlight_end":130}],"label":null,"suggested_replacement":"frame_register","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FrameRegister` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:117\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `frame_register`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FrameOffset` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3938,"byte_end":3949,"line_start":57,"line_end":57,"column_start":140,"column_end":151,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":140,"highlight_end":151}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3938,"byte_end":3949,"line_start":57,"line_end":57,"column_start":140,"column_end":151,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }","highlight_start":140,"highlight_end":151}],"label":null,"suggested_replacement":"frame_offset","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FrameOffset` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:140\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct UNWIND_INFO { pub Version: u8, pub Flags: u8, pub SizeOfProlog: u8, pub CountOfCodes: u8, pub FrameRegister: u8, pub FrameOffset: u8, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `frame_offset`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `BaseAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5163,"byte_end":5174,"line_start":65,"line_end":65,"column_start":54,"column_end":65,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":54,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5163,"byte_end":5174,"line_start":65,"line_end":65,"column_start":54,"column_end":65,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":54,"highlight_end":65}],"label":null,"suggested_replacement":"base_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `BaseAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:54\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u3\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `base_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AllocationBase` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5193,"byte_end":5207,"line_start":65,"line_end":65,"column_start":84,"column_end":98,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":84,"highlight_end":98}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5193,"byte_end":5207,"line_start":65,"line_end":65,"column_start":84,"column_end":98,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":84,"highlight_end":98}],"label":null,"suggested_replacement":"allocation_base","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AllocationBase` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:84\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u3\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `allocation_base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AllocationProtect` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5226,"byte_end":5243,"line_start":65,"line_end":65,"column_start":117,"column_end":134,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":117,"highlight_end":134}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5226,"byte_end":5243,"line_start":65,"line_end":65,"column_start":117,"column_end":134,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":117,"highlight_end":134}],"label":null,"suggested_replacement":"allocation_protect","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AllocationProtect` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:117\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `allocation_protect`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PartitionId` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5254,"byte_end":5265,"line_start":65,"line_end":65,"column_start":145,"column_end":156,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":145,"highlight_end":156}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5254,"byte_end":5265,"line_start":65,"line_end":65,"column_start":145,"column_end":156,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":145,"highlight_end":156}],"label":null,"suggested_replacement":"partition_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PartitionId` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:145\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `partition_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `RegionSize` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5276,"byte_end":5286,"line_start":65,"line_end":65,"column_start":167,"column_end":177,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":167,"highlight_end":177}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5276,"byte_end":5286,"line_start":65,"line_end":65,"column_start":167,"column_end":177,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":167,"highlight_end":177}],"label":null,"suggested_replacement":"region_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `RegionSize` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:167\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `region_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `State` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5299,"byte_end":5304,"line_start":65,"line_end":65,"column_start":190,"column_end":195,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":190,"highlight_end":195}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5299,"byte_end":5304,"line_start":65,"line_end":65,"column_start":190,"column_end":195,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":190,"highlight_end":195}],"label":null,"suggested_replacement":"state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `State` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:190\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Protect` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5315,"byte_end":5322,"line_start":65,"line_end":65,"column_start":206,"column_end":213,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":206,"highlight_end":213}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5315,"byte_end":5322,"line_start":65,"line_end":65,"column_start":206,"column_end":213,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":206,"highlight_end":213}],"label":null,"suggested_replacement":"protect","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Protect` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:206\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `protect`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5333,"byte_end":5337,"line_start":65,"line_end":65,"column_start":224,"column_end":228,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":224,"highlight_end":228}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"rename the identifier or convert it to a snake case raw identifier","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5333,"byte_end":5337,"line_start":65,"line_end":65,"column_start":224,"column_end":228,"is_primary":true,"text":[{"text":"#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }","highlight_start":224,"highlight_end":228}],"label":null,"suggested_replacement":"r#type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Type` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:224\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0motect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: rename the identifier or convert it to a snake case raw identifier\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub \u001b[0m\u001b[0m\u001b[38;5;9mType\u001b[0m\u001b[0m: u32, }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m#[repr(C)] pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub \u001b[0m\u001b[0m\u001b[38;5;10mr#type\u001b[0m\u001b[0m: u32, }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"83 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 83 warnings emitted\u001b[0m\n\n"}
